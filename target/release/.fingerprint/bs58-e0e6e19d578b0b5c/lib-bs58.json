{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"cb58\", \"check\", \"default\", \"sha2\", \"smallvec\", \"std\", \"tinyvec\"]", "target": 2243021261112611720, "profile": 2040997289075261528, "path": 666726909874963799, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/bs58-e0e6e19d578b0b5c/dep-lib-bs58", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}