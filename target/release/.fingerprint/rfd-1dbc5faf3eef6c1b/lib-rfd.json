{"rustc": 15497389221046826682, "features": "[\"ashpd\", \"async-std\", \"default\", \"pollster\", \"urlencoding\", \"xdg-portal\"]", "declared_features": "[\"ashpd\", \"async-std\", \"common-controls-v6\", \"default\", \"file-handle-inner\", \"glib-sys\", \"gobject-sys\", \"gtk-sys\", \"gtk3\", \"pollster\", \"tokio\", \"urlencoding\", \"xdg-portal\"]", "target": 2038336923818351611, "profile": 2040997289075261528, "path": 12241325377456504177, "deps": [[1072176636087918192, "build_script_build", false, 7466266061718940517], [4143744114649553716, "raw_window_handle", false, 17693340226308699180], [5986029879202738730, "log", false, 2766790721061380118], [6662727387107639740, "objc", false, 17166912216634150553], [6839889263267961860, "objc_id", false, 734553174779457779], [11358553063310754093, "block", false, 2372855065914522131], [13650921406158031571, "objc_foundation", false, 8909810391132075552], [16431863889745914764, "dispatch", false, 1335223326228123320]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rfd-1dbc5faf3eef6c1b/dep-lib-rfd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}