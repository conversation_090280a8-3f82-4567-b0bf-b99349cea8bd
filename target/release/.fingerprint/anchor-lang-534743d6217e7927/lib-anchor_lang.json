{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"allow-missing-optionals\", \"anchor-debug\", \"anchor-syn\", \"derive\", \"event-cpi\", \"idl-build\", \"init-if-needed\"]", "target": 8056595410447424620, "profile": 2040997289075261528, "path": 119710664080376573, "deps": [[*****************, "solana_program", false, 16497045240960248346], [*****************, "bincode", false, 16459022946219051993], [1887265696381199940, "anchor_attribute_program", false, 4507333360735982738], [2611905835808443941, "borsh", false, 13576135720719682373], [2872329414160291446, "anchor_attribute_account", false, 1418881526626890433], [5111696347431882526, "anchor_derive_space", false, 8409641380594937519], [5153504383575878548, "anchor_attribute_constant", false, 2385054408930289955], [6350578631969742898, "anchor_derive_accounts", false, 11023186341410452162], [7795340888724370787, "anchor_attribute_access_control", false, 507930923411701064], [8008191657135824715, "thiserror", false, 4106088311053458429], [9529943735784919782, "arrayref", false, 13593828357340443708], [9920160576179037441, "getrandom", false, 2898874249054405284], [11279643347819721421, "anchor_attribute_event", false, 472442858010137647], [14074610438553418890, "bytemuck", false, 13434807616073888886], [14449956356408373298, "anchor_attribute_error", false, 11385402679566813989], [15379490376034388359, "anchor_derive_serde", false, 16359943195072210152], [17282734725213053079, "base64", false, 14680236996356277771]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/anchor-lang-534743d6217e7927/dep-lib-anchor_lang", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}