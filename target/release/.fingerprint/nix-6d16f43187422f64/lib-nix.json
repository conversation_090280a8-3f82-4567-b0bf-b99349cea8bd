{"rustc": 15497389221046826682, "features": "[\"fs\", \"process\", \"signal\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"syslog\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 1600181213338542824, "profile": 2040997289075261528, "path": 16609081994762546642, "deps": [[2924422107542798392, "libc", false, 10846377484841315485], [5150833351789356492, "build_script_build", false, 6320523354607804420], [7896293946984509699, "bitflags", false, 5969233100694737439], [10411997081178400487, "cfg_if", false, 11128363527263356031]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/nix-6d16f43187422f64/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}