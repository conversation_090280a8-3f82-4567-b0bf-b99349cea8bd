{"rustc": 15497389221046826682, "features": "[\"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"stream\", \"tokio-native-tls\"]", "declared_features": "[\"__rustls-tls\", \"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "target": 2433367608443825, "profile": 2040997289075261528, "path": 223962045526148326, "deps": [[5986029879202738730, "log", false, 2766790721061380118], [8258418851280347661, "tungstenite", false, 16030794031900747953], [9538054652646069845, "tokio", false, 270703062434759835], [10629569228670356391, "futures_util", false, 9251527229128225187], [12186126227181294540, "tokio_native_tls", false, 2794414100985396559], [16785601910559813697, "native_tls_crate", false, 8491451109595257049]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tokio-tungstenite-ac779c4bf44a5a14/dep-lib-tokio_tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}