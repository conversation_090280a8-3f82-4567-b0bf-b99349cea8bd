{"$message_type":"diagnostic","message":"unused import: `commitment_config::CommitmentConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/production_jito.rs","byte_start":700,"byte_end":735,"line_start":19,"line_end":19,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    commitment_config::CommitmentConfig,","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/production_jito.rs","byte_start":694,"byte_end":735,"line_start":18,"line_end":19,"column_start":15,"column_end":40,"is_primary":true,"text":[{"text":"    hash::Hash,","highlight_start":15,"highlight_end":16},{"text":"    commitment_config::CommitmentConfig,","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `commitment_config::CommitmentConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/production_jito.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    commitment_config::CommitmentConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/production_jito.rs","byte_start":898,"byte_end":918,"line_start":26,"line_end":26,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    collections::HashMap,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/production_jito.rs","byte_start":892,"byte_end":918,"line_start":25,"line_end":26,"column_start":54,"column_end":25,"is_primary":true,"text":[{"text":"    time::{SystemTime, UNIX_EPOCH, Duration, Instant},","highlight_start":54,"highlight_end":55},{"text":"    collections::HashMap,","highlight_start":1,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/production_jito.rs","byte_start":837,"byte_end":843,"line_start":24,"line_end":25,"column_start":10,"column_end":5,"is_primary":true,"text":[{"text":"use std::{","highlight_start":10,"highlight_end":11},{"text":"    time::{SystemTime, UNIX_EPOCH, Duration, Instant},","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/production_jito.rs","byte_start":918,"byte_end":921,"line_start":26,"line_end":27,"column_start":25,"column_end":2,"is_primary":true,"text":[{"text":"    collections::HashMap,","highlight_start":25,"highlight_end":26},{"text":"};","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `collections::HashMap`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/production_jito.rs:26:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    collections::HashMap,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/periodic_scanner.rs","byte_start":4,"byte_end":18,"line_start":1,"line_end":1,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/periodic_scanner.rs","byte_start":0,"byte_end":20,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use std::time::{Duration, Instant};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow::Result`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/periodic_scanner.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::Result;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `TransactionResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":435,"byte_end":452,"line_start":9,"line_end":9,"column_start":59,"column_end":76,"is_primary":true,"text":[{"text":"use crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};","highlight_start":59,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":433,"byte_end":452,"line_start":9,"line_end":9,"column_start":57,"column_end":76,"is_primary":true,"text":[{"text":"use crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};","highlight_start":57,"highlight_end":76}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":412,"byte_end":413,"line_start":9,"line_end":9,"column_start":36,"column_end":37,"is_primary":true,"text":[{"text":"use crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};","highlight_start":36,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":452,"byte_end":453,"line_start":9,"line_end":9,"column_start":76,"column_end":77,"is_primary":true,"text":[{"text":"use crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};","highlight_start":76,"highlight_end":77}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `TransactionResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/trade_executor.rs:9:59\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ManagedPosition`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":93,"byte_end":108,"line_start":4,"line_end":4,"column_start":48,"column_end":63,"is_primary":true,"text":[{"text":"use crate::position_manager::{PositionManager, ManagedPosition};","highlight_start":48,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":91,"byte_end":108,"line_start":4,"line_end":4,"column_start":46,"column_end":63,"is_primary":true,"text":[{"text":"use crate::position_manager::{PositionManager, ManagedPosition};","highlight_start":46,"highlight_end":63}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":75,"byte_end":76,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::position_manager::{PositionManager, ManagedPosition};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":108,"byte_end":109,"line_start":4,"line_end":4,"column_start":63,"column_end":64,"is_primary":true,"text":[{"text":"use crate::position_manager::{PositionManager, ManagedPosition};","highlight_start":63,"highlight_end":64}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `ManagedPosition`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/position_monitor.rs:4:48\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::position_manager::{PositionManager, ManagedPosition};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/sell_logic.rs","byte_start":4,"byte_end":18,"line_start":1,"line_end":1,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/sell_logic.rs","byte_start":0,"byte_end":20,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow::Result`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/sell_logic.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::Result;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `SystemTime` and `UNIX_EPOCH`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":46,"byte_end":56,"line_start":2,"line_end":2,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration, Instant};","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":58,"byte_end":68,"line_start":2,"line_end":2,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration, Instant};","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":46,"byte_end":70,"line_start":2,"line_end":2,"column_start":17,"column_end":41,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration, Instant};","highlight_start":17,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `SystemTime` and `UNIX_EPOCH`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/dip_monitor.rs:2:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{SystemTime, UNIX_EPOCH, Duration, Instant};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::time::sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":222,"byte_end":240,"line_start":7,"line_end":7,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":218,"byte_end":242,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tokio::time::sleep`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/dip_monitor.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::sleep;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `TradingConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":298,"byte_end":311,"line_start":9,"line_end":9,"column_start":56,"column_end":69,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":56,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":296,"byte_end":311,"line_start":9,"line_end":9,"column_start":54,"column_end":69,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":54,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":282,"byte_end":283,"line_start":9,"line_end":9,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":311,"byte_end":312,"line_start":9,"line_end":9,"column_start":69,"column_end":70,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":69,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `TradingConfig`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/dip_monitor.rs:9:56\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::live_pump_integration_main::{LiveTokenData, TradingConfig};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `account::Account` and `commitment_config::CommitmentConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":334,"byte_end":369,"line_start":10,"line_end":10,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    commitment_config::CommitmentConfig,","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":375,"byte_end":391,"line_start":11,"line_end":11,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    account::Account,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":328,"byte_end":391,"line_start":9,"line_end":11,"column_start":19,"column_end":21,"is_primary":true,"text":[{"text":"    pubkey::Pubkey,","highlight_start":19,"highlight_end":20},{"text":"    commitment_config::CommitmentConfig,","highlight_start":1,"highlight_end":41},{"text":"    account::Account,","highlight_start":1,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `account::Account` and `commitment_config::CommitmentConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    commitment_config::CommitmentConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    account::Account,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::time::sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":578,"byte_end":596,"line_start":20,"line_end":20,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":574,"byte_end":598,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":1,"highlight_end":24},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tokio::time::sleep`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::sleep;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize` and `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":610,"byte_end":621,"line_start":21,"line_end":21,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":623,"byte_end":632,"line_start":21,"line_end":21,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":598,"byte_end":635,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Deserialize` and `Serialize`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:21:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `TradingConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":691,"byte_end":704,"line_start":23,"line_end":23,"column_start":56,"column_end":69,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":56,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":689,"byte_end":704,"line_start":23,"line_end":23,"column_start":54,"column_end":69,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":54,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":675,"byte_end":676,"line_start":23,"line_end":23,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":704,"byte_end":705,"line_start":23,"line_end":23,"column_start":69,"column_end":70,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":69,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `TradingConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:23:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::live_pump_integration_main::{LiveTokenData, TradingConfig};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::pump_api_client::PumpApiClient`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":711,"byte_end":748,"line_start":24,"line_end":24,"column_start":5,"column_end":42,"is_primary":true,"text":[{"text":"use crate::pump_api_client::PumpApiClient;","highlight_start":5,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":707,"byte_end":750,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::pump_api_client::PumpApiClient;","highlight_start":1,"highlight_end":43},{"text":"use crate::chainstack_client::ChainstackRpcClient;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::pump_api_client::PumpApiClient`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:24:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::pump_api_client::PumpApiClient;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::logs_event_processor::TokenCreationInfo`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1075,"byte_end":1121,"line_start":31,"line_end":31,"column_start":5,"column_end":51,"is_primary":true,"text":[{"text":"use crate::logs_event_processor::TokenCreationInfo;","highlight_start":5,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1071,"byte_end":1123,"line_start":31,"line_end":32,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::logs_event_processor::TokenCreationInfo;","highlight_start":1,"highlight_end":52},{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::logs_event_processor::TokenCreationInfo`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:31:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::logs_event_processor::TokenCreationInfo;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `GraduationCandidate`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1177,"byte_end":1196,"line_start":32,"line_end":32,"column_start":55,"column_end":74,"is_primary":true,"text":[{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":55,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1175,"byte_end":1196,"line_start":32,"line_end":32,"column_start":53,"column_end":74,"is_primary":true,"text":[{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":53,"highlight_end":74}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1156,"byte_end":1157,"line_start":32,"line_end":32,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1196,"byte_end":1197,"line_start":32,"line_end":32,"column_start":74,"column_end":75,"is_primary":true,"text":[{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":74,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `GraduationCandidate`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:32:55\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `commitment_config::CommitmentConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":505,"byte_end":540,"line_start":15,"line_end":15,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    commitment_config::CommitmentConfig,","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":499,"byte_end":540,"line_start":14,"line_end":15,"column_start":23,"column_end":40,"is_primary":true,"text":[{"text":"    system_instruction,","highlight_start":23,"highlight_end":24},{"text":"    commitment_config::CommitmentConfig,","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `commitment_config::CommitmentConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    commitment_config::CommitmentConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Instant`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":689,"byte_end":696,"line_start":21,"line_end":21,"column_start":46,"column_end":53,"is_primary":true,"text":[{"text":"    time::{SystemTime, UNIX_EPOCH, Duration, Instant},","highlight_start":46,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":687,"byte_end":696,"line_start":21,"line_end":21,"column_start":44,"column_end":53,"is_primary":true,"text":[{"text":"    time::{SystemTime, UNIX_EPOCH, Duration, Instant},","highlight_start":44,"highlight_end":53}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Instant`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:21:46\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    time::{SystemTime, UNIX_EPOCH, Duration, Instant},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `sync::Mutex` and `time::sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":795,"byte_end":806,"line_start":27,"line_end":27,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    time::sleep,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":812,"byte_end":823,"line_start":28,"line_end":28,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    sync::Mutex,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":795,"byte_end":829,"line_start":27,"line_end":29,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    time::sleep,","highlight_start":5,"highlight_end":17},{"text":"    sync::Mutex,","highlight_start":1,"highlight_end":17},{"text":"    signal,","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":789,"byte_end":795,"line_start":26,"line_end":27,"column_start":12,"column_end":5,"is_primary":true,"text":[{"text":"use tokio::{","highlight_start":12,"highlight_end":13},{"text":"    time::sleep,","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":835,"byte_end":838,"line_start":29,"line_end":30,"column_start":11,"column_end":2,"is_primary":true,"text":[{"text":"    signal,","highlight_start":11,"highlight_end":12},{"text":"};","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `sync::Mutex` and `time::sleep`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:27:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    time::sleep,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sync::Mutex,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rand`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":844,"byte_end":848,"line_start":31,"line_end":31,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"use rand;","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":840,"byte_end":850,"line_start":31,"line_end":32,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rand;","highlight_start":1,"highlight_end":10},{"text":"// use futures_util::{SinkExt, StreamExt}; // Disabled for performance","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:31:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `connect_async` and `tungstenite::protocol::Message`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":945,"byte_end":958,"line_start":33,"line_end":33,"column_start":25,"column_end":38,"is_primary":true,"text":[{"text":"use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};","highlight_start":25,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":960,"byte_end":990,"line_start":33,"line_end":33,"column_start":40,"column_end":70,"is_primary":true,"text":[{"text":"use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};","highlight_start":40,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":921,"byte_end":993,"line_start":33,"line_end":34,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};","highlight_start":1,"highlight_end":72},{"text":"use crate::production_jito::{ProductionJitoClient, TradingPriority};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `connect_async` and `tungstenite::protocol::Message`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:33:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio_tungstenite::{connect_async, tungstenite::protocol::Message};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/performance_dashboard.rs","byte_start":434,"byte_end":442,"line_start":11,"line_end":11,"column_start":41,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration};","highlight_start":41,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/performance_dashboard.rs","byte_start":432,"byte_end":442,"line_start":11,"line_end":11,"column_start":39,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration};","highlight_start":39,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Duration`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/performance_dashboard.rs:11:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{SystemTime, UNIX_EPOCH, Duration};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/pump_instruction_builder.rs","byte_start":227,"byte_end":233,"line_start":6,"line_end":6,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/pump_instruction_builder.rs","byte_start":225,"byte_end":233,"line_start":6,"line_end":6,"column_start":20,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":20,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/pump_instruction_builder.rs","byte_start":218,"byte_end":219,"line_start":6,"line_end":6,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/pump_instruction_builder.rs","byte_start":233,"byte_end":234,"line_start":6,"line_end":6,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/pump_instruction_builder.rs:6:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::{Result, anyhow};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `sysvar`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/pump_instruction_builder.rs","byte_start":343,"byte_end":349,"line_start":11,"line_end":11,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    sysvar,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/pump_instruction_builder.rs","byte_start":337,"byte_end":349,"line_start":10,"line_end":11,"column_start":19,"column_end":11,"is_primary":true,"text":[{"text":"    system_program,","highlight_start":19,"highlight_end":20},{"text":"    sysvar,","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `sysvar`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/pump_instruction_builder.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sysvar,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `commitment_config::CommitmentConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/priority_fees_executor.rs","byte_start":424,"byte_end":459,"line_start":13,"line_end":13,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    commitment_config::CommitmentConfig,","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/priority_fees_executor.rs","byte_start":418,"byte_end":459,"line_start":12,"line_end":13,"column_start":45,"column_end":40,"is_primary":true,"text":[{"text":"    compute_budget::ComputeBudgetInstruction,","highlight_start":45,"highlight_end":46},{"text":"    commitment_config::CommitmentConfig,","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `commitment_config::CommitmentConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/priority_fees_executor.rs:13:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    commitment_config::CommitmentConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Result` and `anyhow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/position_state_machine.rs","byte_start":485,"byte_end":491,"line_start":13,"line_end":13,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/position_state_machine.rs","byte_start":493,"byte_end":499,"line_start":13,"line_end":13,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/position_state_machine.rs","byte_start":472,"byte_end":502,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":1,"highlight_end":30},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Result` and `anyhow`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/position_state_machine.rs:13:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::{Result, anyhow};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variant `TRAILING_ACTIVE` should have an upper camel case name","code":{"code":"non_camel_case_types","explanation":null},"level":"warning","spans":[{"file_name":"src/position_state_machine.rs","byte_start":893,"byte_end":908,"line_start":27,"line_end":27,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    TRAILING_ACTIVE {","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_camel_case_types)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to upper camel case","code":null,"level":"help","spans":[{"file_name":"src/position_state_machine.rs","byte_start":893,"byte_end":908,"line_start":27,"line_end":27,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    TRAILING_ACTIVE {","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"TrailingActive","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variant `TRAILING_ACTIVE` should have an upper camel case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/position_state_machine.rs:27:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    TRAILING_ACTIVE {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to upper camel case: `TrailingActive`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(non_camel_case_types)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variant `SELL_PENDING` should have an upper camel case name","code":{"code":"non_camel_case_types","explanation":null},"level":"warning","spans":[{"file_name":"src/position_state_machine.rs","byte_start":1270,"byte_end":1282,"line_start":39,"line_end":39,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    SELL_PENDING {","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to upper camel case","code":null,"level":"help","spans":[{"file_name":"src/position_state_machine.rs","byte_start":1270,"byte_end":1282,"line_start":39,"line_end":39,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    SELL_PENDING {","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"SellPending","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variant `SELL_PENDING` should have an upper camel case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/position_state_machine.rs:39:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    SELL_PENDING {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to upper camel case: `SellPending`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variant `SELL_EXECUTING` should have an upper camel case name","code":{"code":"non_camel_case_types","explanation":null},"level":"warning","spans":[{"file_name":"src/position_state_machine.rs","byte_start":1518,"byte_end":1532,"line_start":48,"line_end":48,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    SELL_EXECUTING {","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to upper camel case","code":null,"level":"help","spans":[{"file_name":"src/position_state_machine.rs","byte_start":1518,"byte_end":1532,"line_start":48,"line_end":48,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    SELL_EXECUTING {","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":"SellExecuting","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variant `SELL_EXECUTING` should have an upper camel case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/position_state_machine.rs:48:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    SELL_EXECUTING {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to upper camel case: `SellExecuting`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/chainstack_platform_client.rs","byte_start":246,"byte_end":250,"line_start":8,"line_end":8,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/chainstack_platform_client.rs","byte_start":246,"byte_end":252,"line_start":8,"line_end":8,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/chainstack_platform_client.rs","byte_start":245,"byte_end":246,"line_start":8,"line_end":8,"column_start":17,"column_end":18,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":17,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/chainstack_platform_client.rs","byte_start":257,"byte_end":258,"line_start":8,"line_end":8,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `json`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/chainstack_platform_client.rs:8:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::{json, Value};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `signature::Signature` and `transaction::Transaction`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/multi_provider_client.rs","byte_start":405,"byte_end":425,"line_start":12,"line_end":12,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    signature::Signature,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/multi_provider_client.rs","byte_start":431,"byte_end":455,"line_start":13,"line_end":13,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"    transaction::Transaction,","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/multi_provider_client.rs","byte_start":399,"byte_end":455,"line_start":11,"line_end":13,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"    account::Account,","highlight_start":21,"highlight_end":22},{"text":"    signature::Signature,","highlight_start":1,"highlight_end":26},{"text":"    transaction::Transaction,","highlight_start":1,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `signature::Signature` and `transaction::Transaction`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multi_provider_client.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    signature::Signature,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    transaction::Transaction,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::time::sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/multi_provider_client.rs","byte_start":537,"byte_end":555,"line_start":17,"line_end":17,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/multi_provider_client.rs","byte_start":533,"byte_end":557,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tokio::time::sleep`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/multi_provider_client.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::sleep;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize` and `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/shyft_client.rs","byte_start":364,"byte_end":375,"line_start":8,"line_end":8,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/shyft_client.rs","byte_start":377,"byte_end":386,"line_start":8,"line_end":8,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/shyft_client.rs","byte_start":352,"byte_end":389,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37},{"text":"use solana_sdk::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Deserialize` and `Serialize`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/shyft_client.rs:8:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Engine as _` and `engine::general_purpose`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/shyft_client.rs","byte_start":488,"byte_end":499,"line_start":14,"line_end":14,"column_start":14,"column_end":25,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":14,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/shyft_client.rs","byte_start":501,"byte_end":524,"line_start":14,"line_end":14,"column_start":27,"column_end":50,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":27,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/shyft_client.rs","byte_start":475,"byte_end":527,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":1,"highlight_end":52},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Engine as _` and `engine::general_purpose`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/shyft_client.rs:14:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse base64::{Engine as _, engine::general_purpose};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":176,"byte_end":181,"line_start":6,"line_end":6,"column_start":19,"column_end":24,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, timeout};","highlight_start":19,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":176,"byte_end":183,"line_start":6,"line_end":6,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, timeout};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/logs_listener.rs","byte_start":175,"byte_end":176,"line_start":6,"line_end":6,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, timeout};","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/logs_listener.rs","byte_start":190,"byte_end":191,"line_start":6,"line_end":6,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, timeout};","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `sleep`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:6:19\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::{sleep, timeout};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/websocket_tester.rs","byte_start":13,"byte_end":19,"line_start":1,"line_end":1,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"use anyhow::{anyhow, Result};","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/websocket_tester.rs","byte_start":13,"byte_end":21,"line_start":1,"line_end":1,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use anyhow::{anyhow, Result};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/websocket_tester.rs","byte_start":12,"byte_end":13,"line_start":1,"line_end":1,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use anyhow::{anyhow, Result};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/websocket_tester.rs","byte_start":27,"byte_end":28,"line_start":1,"line_end":1,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"use anyhow::{anyhow, Result};","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/websocket_tester.rs:1:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::{anyhow, Result};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/transaction_timing.rs","byte_start":247,"byte_end":255,"line_start":7,"line_end":7,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/transaction_timing.rs","byte_start":247,"byte_end":257,"line_start":7,"line_end":7,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/transaction_timing.rs","byte_start":246,"byte_end":247,"line_start":7,"line_end":7,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/transaction_timing.rs","byte_start":264,"byte_end":265,"line_start":7,"line_end":7,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/transaction_timing.rs:7:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{Duration, Instant};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `DexScreenerTrade`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/external_dashboard.rs","byte_start":480,"byte_end":496,"line_start":13,"line_end":13,"column_start":74,"column_end":90,"is_primary":true,"text":[{"text":"use crate::dexscreener_client::{DexScreenerClient, WalletTradingSummary, DexScreenerTrade};","highlight_start":74,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/external_dashboard.rs","byte_start":478,"byte_end":496,"line_start":13,"line_end":13,"column_start":72,"column_end":90,"is_primary":true,"text":[{"text":"use crate::dexscreener_client::{DexScreenerClient, WalletTradingSummary, DexScreenerTrade};","highlight_start":72,"highlight_end":90}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `DexScreenerTrade`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/external_dashboard.rs:13:74\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::dexscreener_client::{DexScreenerClient, WalletTradingSummary, DexScreenerTrade};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::net::TcpListener`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/enhanced_dashboard.rs","byte_start":397,"byte_end":420,"line_start":11,"line_end":11,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use tokio::net::TcpListener;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/enhanced_dashboard.rs","byte_start":393,"byte_end":422,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::net::TcpListener;","highlight_start":1,"highlight_end":29},{"text":"use warp::Filter;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tokio::net::TcpListener`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/enhanced_dashboard.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::net::TcpListener;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `tx_result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":16768,"byte_end":16777,"line_start":345,"line_end":345,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                Ok(tx_result) => {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":16768,"byte_end":16777,"line_start":345,"line_end":345,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                Ok(tx_result) => {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":"_tx_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `tx_result`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/trade_executor.rs:345:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m345\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Ok(tx_result) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_tx_result`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `tx_result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":23843,"byte_end":23852,"line_start":489,"line_end":489,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                Ok(tx_result) => {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":23843,"byte_end":23852,"line_start":489,"line_end":489,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                Ok(tx_result) => {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":"_tx_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `tx_result`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/trade_executor.rs:489:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m489\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Ok(tx_result) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_tx_result`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `position_manager` of struct `LivePumpFunClient` is private","code":{"code":"E0616","explanation":"Attempted to access a private field on a struct.\n\nErroneous code example:\n\n```compile_fail,E0616\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // error: field `x` of struct `some_module::Foo` is private\n```\n\nIf you want to access this field, you have two options:\n\n1) Set the field public:\n\n```\nmod some_module {\n    pub struct Foo {\n        pub x: u32, // `x` is now public.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // ok!\n```\n\n2) Add a getter function:\n\n```\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is still private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n\n        // We create the getter function here:\n        pub fn get_x(&self) -> &u32 { &self.x }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.get_x()); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/live_pump_integration/handoff_fix.rs","byte_start":1495,"byte_end":1511,"line_start":35,"line_end":35,"column_start":51,"column_end":67,"is_primary":true,"text":[{"text":"                let has_active_positions = client.position_manager.has_active_positions().await;","highlight_start":51,"highlight_end":67}],"label":"private field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0616]\u001b[0m\u001b[0m\u001b[1m: field `position_manager` of struct `LivePumpFunClient` is private\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/handoff_fix.rs:35:51\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let has_active_positions = client.position_manager.has_active_positions().await;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate field\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `token_tx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":31350,"byte_end":31358,"line_start":757,"line_end":757,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"        let (token_tx, mut token_rx) = mpsc::channel::<TokenCreationEvent>(100);","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":31350,"byte_end":31358,"line_start":757,"line_end":757,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"        let (token_tx, mut token_rx) = mpsc::channel::<TokenCreationEvent>(100);","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"_token_tx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `token_tx`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:757:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m757\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let (token_tx, mut token_rx) = mpsc::channel::<TokenCreationEvent>(100);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_token_tx`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `trade_tx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":31431,"byte_end":31439,"line_start":758,"line_end":758,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"        let (trade_tx, mut trade_rx) = mpsc::channel::<TradeEvent>(1000);","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":31431,"byte_end":31439,"line_start":758,"line_end":758,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"        let (trade_tx, mut trade_rx) = mpsc::channel::<TradeEvent>(1000);","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"_trade_tx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `trade_tx`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:758:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m758\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let (trade_tx, mut trade_rx) = mpsc::channel::<TradeEvent>(1000);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_trade_tx`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `progress`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":73619,"byte_end":73627,"line_start":1578,"line_end":1578,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let progress = 0.0; // New tokens start at 0% progress","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":73619,"byte_end":73627,"line_start":1578,"line_end":1578,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let progress = 0.0; // New tokens start at 0% progress","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_progress","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `progress`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:1578:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1578\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let progress = 0.0; // New tokens start at 0% progress\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_progress`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `sol_reserves`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":73682,"byte_end":73694,"line_start":1579,"line_end":1579,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let sol_reserves = token_event.liquidity_sol;","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":73682,"byte_end":73694,"line_start":1579,"line_end":1579,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let sol_reserves = token_event.liquidity_sol;","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_sol_reserves","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `sol_reserves`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:1579:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1579\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let sol_reserves = token_event.liquidity_sol;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_sol_reserves`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `market_cap`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":73736,"byte_end":73746,"line_start":1580,"line_end":1580,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let market_cap = token_event.market_cap_sol * self.trading_config.sol_to_usd_rate;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":73736,"byte_end":73746,"line_start":1580,"line_end":1580,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let market_cap = token_event.market_cap_sol * self.trading_config.sol_to_usd_rate;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_market_cap","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `market_cap`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:1580:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1580\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let market_cap = token_event.market_cap_sol * self.trading_config.sol_to_usd_rate;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_market_cap`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `status`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":95172,"byte_end":95178,"line_start":2005,"line_end":2005,"column_start":25,"column_end":31,"is_primary":true,"text":[{"text":"                    let status = match exit_reason {","highlight_start":25,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":95172,"byte_end":95178,"line_start":2005,"line_end":2005,"column_start":25,"column_end":31,"is_primary":true,"text":[{"text":"                    let status = match exit_reason {","highlight_start":25,"highlight_end":31}],"label":null,"suggested_replacement":"_status","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `status`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:2005:25\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2005\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let status = match exit_reason {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_status`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `base_delay`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":103039,"byte_end":103049,"line_start":2162,"line_end":2162,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let base_delay = tokio::time::Duration::from_secs(10); // Start with 10 second delays","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":103039,"byte_end":103049,"line_start":2162,"line_end":2162,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let base_delay = tokio::time::Duration::from_secs(10); // Start with 10 second delays","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_base_delay","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `base_delay`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:2162:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let base_delay = tokio::time::Duration::from_secs(10); // Start with 10 second delays\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_base_delay`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `signature`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/priority_fees_executor.rs","byte_start":11682,"byte_end":11691,"line_start":293,"line_end":293,"column_start":49,"column_end":58,"is_primary":true,"text":[{"text":"    pub async fn get_transaction_details(&self, signature: &Signature) -> Result<Option<solana_transaction_status::UiTransactionEncoding>> {","highlight_start":49,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/priority_fees_executor.rs","byte_start":11682,"byte_end":11691,"line_start":293,"line_end":293,"column_start":49,"column_end":58,"is_primary":true,"text":[{"text":"    pub async fn get_transaction_details(&self, signature: &Signature) -> Result<Option<solana_transaction_status::UiTransactionEncoding>> {","highlight_start":49,"highlight_end":58}],"label":null,"suggested_replacement":"_signature","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `signature`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/priority_fees_executor.rs:293:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_transaction_details(&self, signature: &Signature) -> Result<Option<solana_transaction_status::UiTransactionEncoding>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_signature`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/chainstack_client.rs","byte_start":5659,"byte_end":5665,"line_start":154,"line_end":154,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"        let result = response_json.get(\"result\")","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/chainstack_client.rs","byte_start":5659,"byte_end":5665,"line_start":154,"line_end":154,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"        let result = response_json.get(\"result\")","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":"_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `result`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/chainstack_client.rs:154:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let result = response_json.get(\"result\")\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_result`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":10023,"byte_end":10035,"line_start":242,"line_end":242,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let current_time = std::time::SystemTime::now()","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":10023,"byte_end":10035,"line_start":242,"line_end":242,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let current_time = std::time::SystemTime::now()","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_current_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `current_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:242:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let current_time = std::time::SystemTime::now()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_current_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `min_age_seconds`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":11231,"byte_end":11246,"line_start":270,"line_end":270,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        min_age_seconds: u64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":11231,"byte_end":11246,"line_start":270,"line_end":270,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        min_age_seconds: u64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"_min_age_seconds","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `min_age_seconds`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:270:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        min_age_seconds: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_min_age_seconds`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `max_age_seconds`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":11261,"byte_end":11276,"line_start":271,"line_end":271,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        max_age_seconds: u64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":11261,"byte_end":11276,"line_start":271,"line_end":271,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        max_age_seconds: u64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"_max_age_seconds","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `max_age_seconds`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:271:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m271\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        max_age_seconds: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_max_age_seconds`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `min_progress`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":11291,"byte_end":11303,"line_start":272,"line_end":272,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        min_progress: f64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":11291,"byte_end":11303,"line_start":272,"line_end":272,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        min_progress: f64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_min_progress","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `min_progress`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:272:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        min_progress: f64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_min_progress`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `max_progress`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":11318,"byte_end":11330,"line_start":273,"line_end":273,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        max_progress: f64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":11318,"byte_end":11330,"line_start":273,"line_end":273,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        max_progress: f64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_max_progress","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `max_progress`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:273:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m273\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        max_progress: f64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_max_progress`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `response`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/websocket_tester.rs","byte_start":3750,"byte_end":3758,"line_start":104,"line_end":104,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"        let (ws_stream, response) = match connect_result {","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/websocket_tester.rs","byte_start":3750,"byte_end":3758,"line_start":104,"line_end":104,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"        let (ws_stream, response) = match connect_result {","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":"_response","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `response`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/websocket_tester.rs:104:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let (ws_stream, response) = match connect_result {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_response`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `refresh_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/enhanced_dashboard.rs","byte_start":3296,"byte_end":3310,"line_start":100,"line_end":100,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"        let refresh_handle = tokio::spawn(async move {","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/enhanced_dashboard.rs","byte_start":3296,"byte_end":3310,"line_start":100,"line_end":100,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"        let refresh_handle = tokio::spawn(async move {","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":"_refresh_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `refresh_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/enhanced_dashboard.rs:100:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let refresh_handle = tokio::spawn(async move {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_refresh_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `mature_min_velocity`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/filters.rs","byte_start":7289,"byte_end":7308,"line_start":165,"line_end":165,"column_start":13,"column_end":32,"is_primary":true,"text":[{"text":"        let mature_min_velocity = std::env::var(\"PUMP_VELOCITY_EXEMPTION_MATURE_MIN_VELOCITY\")","highlight_start":13,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/filters.rs","byte_start":7289,"byte_end":7308,"line_start":165,"line_end":165,"column_start":13,"column_end":32,"is_primary":true,"text":[{"text":"        let mature_min_velocity = std::env::var(\"PUMP_VELOCITY_EXEMPTION_MATURE_MIN_VELOCITY\")","highlight_start":13,"highlight_end":32}],"label":null,"suggested_replacement":"_mature_min_velocity","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `mature_min_velocity`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/filters.rs:165:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mature_min_velocity = std::env::var(\"PUMP_VELOCITY_EXEMPTION_MATURE_MIN_VELOCITY\")\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_mature_min_velocity`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `old_min_velocity`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/filters.rs","byte_start":7745,"byte_end":7761,"line_start":175,"line_end":175,"column_start":13,"column_end":29,"is_primary":true,"text":[{"text":"        let old_min_velocity = std::env::var(\"PUMP_VELOCITY_EXEMPTION_OLD_MIN_VELOCITY\")","highlight_start":13,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/filters.rs","byte_start":7745,"byte_end":7761,"line_start":175,"line_end":175,"column_start":13,"column_end":29,"is_primary":true,"text":[{"text":"        let old_min_velocity = std::env::var(\"PUMP_VELOCITY_EXEMPTION_OLD_MIN_VELOCITY\")","highlight_start":13,"highlight_end":29}],"label":null,"suggested_replacement":"_old_min_velocity","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `old_min_velocity`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/filters.rs:175:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m175\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let old_min_velocity = std::env::var(\"PUMP_VELOCITY_EXEMPTION_OLD_MIN_VELOCITY\")\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_old_min_velocity`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `is_immediate_candidate`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":97731,"byte_end":97753,"line_start":2042,"line_end":2042,"column_start":13,"column_end":35,"is_primary":true,"text":[{"text":"        let is_immediate_candidate = if let Some(ref aging_pipeline) = self.aging_pipeline {","highlight_start":13,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":97731,"byte_end":97753,"line_start":2042,"line_end":2042,"column_start":13,"column_end":35,"is_primary":true,"text":[{"text":"        let is_immediate_candidate = if let Some(ref aging_pipeline) = self.aging_pipeline {","highlight_start":13,"highlight_end":35}],"label":null,"suggested_replacement":"_is_immediate_candidate","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `is_immediate_candidate`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:2042:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2042\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let is_immediate_candidate = if let Some(ref aging_pipeline) = self.aging_pipeline {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_is_immediate_candidate`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 60 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error; 60 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0616`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0616`.\u001b[0m\n"}
