{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 775285158232319892, "profile": 2040997289075261528, "path": 4116893179673126486, "deps": [[*****************, "solana_program", false, 16497045240960248346], [7479230097745373958, "spl_type_length_value", false, 18429861239003583430], [9529943735784919782, "arrayref", false, 13593828357340443708], [11699822774991256268, "spl_pod", false, 9875525080450620145], [12414676574469740085, "spl_tlv_account_resolution", false, 9078493240593981780], [14074610438553418890, "bytemuck", false, 13434807616073888886], [14673743079976092479, "spl_program_error", false, 12610852475290071081], [18269786033916185670, "spl_discriminator", false, 681856103427276452]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/spl-transfer-hook-interface-946fd97cf16bda93/dep-lib-spl_transfer_hook_interface", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}